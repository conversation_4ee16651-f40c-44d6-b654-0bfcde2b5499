2025-08-03 09:28:34 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-08-03 09:28:34 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-08-03 09:28:34 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-08-03 09:28:34 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-08-03 09:28:34.748 [INFO] [exchanges.okx_exchange] 🚨 OKX API限制优化为2次/秒，解决WebSocket阻塞问题
2025-08-03 09:28:34.748 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-08-03 09:28:34 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-08-03 09:28:34 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-08-03 09:28:34 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-08-03 09:28:35 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-08-03 09:28:35.201 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:28:35.518 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:28:35.518 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:28:35.855 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:28:35.855 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:28:35 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-08-03 09:28:35.855 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:28:36.188 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:28:36.188 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:28:36.520 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:28:36.520 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:28:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-08-03 09:28:36.520 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:28:36.842 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:28:36.842 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:28:37.188 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:28:37.188 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:28:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-08-03 09:28:37.188 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:28:37.509 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:28:37.509 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:28:37.847 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:28:37.847 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:28:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-08-03 09:28:37 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-08-03 09:28:38.161 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 09:28:39.548 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 09:28:41.038 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:28:41.706 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:28:42.289 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:28:42.608 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:28:43.181 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:28:43.751 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:29:03.610 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:03.610 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:04.284 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:04.284 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:04.929 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:04.929 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:05.848 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:05.848 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:06.256 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:06.256 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:06.914 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:06.914 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:49.262 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 09:29:49.590 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 09:29:49.613 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-08-03 09:29:49.618 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:29:49.633 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:29:52.032 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-08-03 09:29:52.366 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-08-03 09:29:52.373 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-08-03 09:29:52.383 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-08-03 09:29:52.391 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-08-03 09:29:53.393 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.727 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.727 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.727 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.728 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.728 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.728 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.728 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.728 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.728 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-08-03 09:29:53.801 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:53.802 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:54.139 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:54.139 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:54.140 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:54.141 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:54.144 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:54.144 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:54.149 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.150 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 09:29:54.153 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:54.153 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:54.162 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.162 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.162 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.163 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 09:29:54.165 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:54.165 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:54.167 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.167 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.168 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.172 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.173 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 09:29:54.203 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-08-03 09:29:54.204 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-08-03 09:29:54.205 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-08-03 09:29:54.205 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-08-03 09:29:54 [WARNING] [OKXExchange] 🚨 OKX API限速错误，2.0秒后重试 (尝试 1/4)
2025-08-03 09:29:54.470 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:54.470 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:54.471 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:54.471 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:54.472 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:54.472 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:54.477 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:54.477 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:56.283 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:56.283 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:56.557 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:56.558 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:56.560 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:56.560 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:56.560 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:56.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:56.561 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '*****************', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '*****************'}
2025-08-03 09:29:56.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-08-03 09:29:56.589 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:56.589 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:56.884 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:56.885 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:56.890 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:56.890 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:56.897 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:56.898 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:29:56.898 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-08-03 09:29:56.898 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-08-03 09:30:08.151 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 09:30:09.645 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 09:30:11.165 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:30:11.813 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:30:12.392 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:30:12.723 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:30:13.298 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:30:13.871 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:30:18.657 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-08-03 09:30:20.204 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-08-03 09:30:21.659 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:30:22.328 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:30:22.914 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-08-03 09:30:23.248 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:30:23.823 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-08-03 09:30:24.401 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
